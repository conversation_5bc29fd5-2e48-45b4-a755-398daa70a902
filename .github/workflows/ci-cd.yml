# .github/workflows/ci-cd.yml
# Comprehensive CI/CD pipeline with quality gates

name: CI/CD Pipeline

on:
  push:
    branches: [main, develop]
  pull_request:
    branches: [main, develop]
  schedule:
    # Run nightly builds
    - cron: '0 2 * * *'

env:
  NODE_VERSION: '18'
  JAVA_VERSION: '11'
  RUBY_VERSION: '3.0'

jobs:
  # Code Quality and Linting
  code-quality:
    name: Code Quality & Linting
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Run ESLint
        run: npm run lint:check
        continue-on-error: false

      - name: Run Prettier
        run: npm run format:check
        continue-on-error: false

      - name: Run TypeScript check
        run: npm run type-check
        continue-on-error: false

      - name: Run dependency audit
        run: npm audit --audit-level=moderate
        continue-on-error: true

      - name: Check bundle size
        run: npm run bundle-analyzer
        continue-on-error: true

      - name: Upload lint results
        uses: actions/upload-artifact@v3
        if: always()
        with:
          name: lint-results
          path: |
            eslint-report.json
            prettier-report.json

  # Unit and Integration Tests
  test-unit:
    name: Unit & Integration Tests
    runs-on: ubuntu-latest
    strategy:
      matrix:
        node-version: ['16', '18', '20']
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js ${{ matrix.node-version }}
        uses: actions/setup-node@v4
        with:
          node-version: ${{ matrix.node-version }}
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Run unit tests
        run: npm run test:unit -- --coverage --watchAll=false
        env:
          CI: true

      - name: Run integration tests
        run: npm run test:integration -- --coverage --watchAll=false
        env:
          CI: true

      - name: Upload coverage to Codecov
        uses: codecov/codecov-action@v3
        with:
          file: ./coverage/lcov.info
          flags: unittests
          name: codecov-umbrella
          fail_ci_if_error: true

      - name: Upload test results
        uses: actions/upload-artifact@v3
        if: always()
        with:
          name: test-results-${{ matrix.node-version }}
          path: |
            coverage/
            junit.xml

  # Performance Tests
  test-performance:
    name: Performance Tests
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Run performance tests
        run: npm run test:performance
        env:
          CI: true

      - name: Upload performance results
        uses: actions/upload-artifact@v3
        with:
          name: performance-results
          path: performance-report.json

  # E2E Tests
  test-e2e:
    name: E2E Tests
    runs-on: macos-latest
    strategy:
      matrix:
        platform: ['ios', 'android']
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Setup Java (Android)
        if: matrix.platform == 'android'
        uses: actions/setup-java@v3
        with:
          java-version: ${{ env.JAVA_VERSION }}
          distribution: 'temurin'

      - name: Setup Ruby (iOS)
        if: matrix.platform == 'ios'
        uses: ruby/setup-ruby@v1
        with:
          ruby-version: ${{ env.RUBY_VERSION }}
          bundler-cache: true

      - name: Install dependencies
        run: npm ci

      - name: Install Detox CLI
        run: npm install -g detox-cli

      - name: Cache Pods (iOS)
        if: matrix.platform == 'ios'
        uses: actions/cache@v3
        with:
          path: ios/Pods
          key: ${{ runner.os }}-pods-${{ hashFiles('ios/Podfile.lock') }}
          restore-keys: |
            ${{ runner.os }}-pods-

      - name: Install Pods (iOS)
        if: matrix.platform == 'ios'
        run: cd ios && pod install

      - name: Build app for testing
        run: detox build --configuration ${{ matrix.platform }}.sim.release

      - name: Run E2E tests
        run: detox test --configuration ${{ matrix.platform }}.sim.release --cleanup
        env:
          CI: true

      - name: Upload E2E artifacts
        uses: actions/upload-artifact@v3
        if: always()
        with:
          name: e2e-artifacts-${{ matrix.platform }}
          path: |
            artifacts/
            detox-report.json

  # Security Scanning
  security-scan:
    name: Security Scanning
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Run security audit
        run: npm audit --audit-level=high
        continue-on-error: true

      - name: Run Snyk security scan
        uses: snyk/actions/node@master
        env:
          SNYK_TOKEN: ${{ secrets.SNYK_TOKEN }}
        with:
          args: --severity-threshold=high
        continue-on-error: true

      - name: Run CodeQL analysis
        uses: github/codeql-action/init@v2
        with:
          languages: javascript

      - name: Perform CodeQL analysis
        uses: github/codeql-action/analyze@v2

  # Quality Gates
  quality-gate:
    name: Quality Gate
    runs-on: ubuntu-latest
    needs: [code-quality, test-unit, test-performance, security-scan]
    if: always()
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Download test artifacts
        uses: actions/download-artifact@v3
        with:
          path: artifacts/

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Run quality gate checks
        run: npm run quality-gate
        env:
          CI: true

      - name: Generate quality report
        run: npm run generate-quality-report

      - name: Upload quality report
        uses: actions/upload-artifact@v3
        with:
          name: quality-report
          path: quality-report.html

      - name: Comment PR with quality results
        if: github.event_name == 'pull_request'
        uses: actions/github-script@v6
        with:
          script: |
            const fs = require('fs');
            const path = './quality-summary.md';
            if (fs.existsSync(path)) {
              const summary = fs.readFileSync(path, 'utf8');
              github.rest.issues.createComment({
                issue_number: context.issue.number,
                owner: context.repo.owner,
                repo: context.repo.repo,
                body: summary
              });
            }

  # Build and Deploy (only on main branch)
  build-deploy:
    name: Build & Deploy
    runs-on: ubuntu-latest
    needs: [quality-gate, test-e2e]
    if: github.ref == 'refs/heads/main' && github.event_name == 'push'
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Build production bundle
        run: npm run build:production
        env:
          CI: true

      - name: Upload build artifacts
        uses: actions/upload-artifact@v3
        with:
          name: production-build
          path: |
            dist/
            android/app/build/outputs/
            ios/build/

      - name: Deploy to staging
        if: github.ref == 'refs/heads/develop'
        run: npm run deploy:staging
        env:
          DEPLOY_TOKEN: ${{ secrets.STAGING_DEPLOY_TOKEN }}

      - name: Deploy to production
        if: github.ref == 'refs/heads/main'
        run: npm run deploy:production
        env:
          DEPLOY_TOKEN: ${{ secrets.PRODUCTION_DEPLOY_TOKEN }}

  # Notification
  notify:
    name: Notify Results
    runs-on: ubuntu-latest
    needs: [build-deploy]
    if: always()
    steps:
      - name: Notify Slack on success
        if: success()
        uses: 8398a7/action-slack@v3
        with:
          status: success
          channel: '#ci-cd'
          text: '✅ CI/CD pipeline completed successfully!'
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}

      - name: Notify Slack on failure
        if: failure()
        uses: 8398a7/action-slack@v3
        with:
          status: failure
          channel: '#ci-cd'
          text: '❌ CI/CD pipeline failed!'
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}
