// src/hooks/useHealthProfileStepCompletion.ts
// Shared hook for unified health profile step completion and navigation
import { useCallback } from 'react';
import { Alert } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { useNavigation } from '@react-navigation/native';
import { useHealthProfile } from '../hooks/useHealthProfile';

// Section key mapping for health profile schema
const sectionKeyMap: Record<string, string> = {
  demographics: 'demographics',
  health_goals: 'healthGoals',
  health_conditions: 'healthConditions',
  allergies: 'allergiesAndSensitivities',
  privacy_consent: 'privacySettings',
};

export function useHealthProfileStepCompletion() {
  const navigation = useNavigation();
  const { updateProfile } = useHealthProfile();

  // Unified step completion logic - simplified to work with individual screens
  const sharedStepCompletion = useCallback(
    async (stepId: string, stepData: any) => {
      console.log(`📝 Step completion: ${stepId} = true`, stepData ? 'with data' : 'no data');

      try {
        // 1. Update setupData if data is provided
        if (stepData) {
          const savedSetupData = await AsyncStorage.getItem('health_profile_setup_data');
          const setupData = savedSetupData ? JSON.parse(savedSetupData) : {};
          setupData[stepId] = stepData;
          await AsyncStorage.setItem('health_profile_setup_data', JSON.stringify(setupData));
          console.log(`💾 Saved ${stepId} data to setup storage`);
        }

        // 2. Update progress in AsyncStorage
        const savedProgress = await AsyncStorage.getItem('health_profile_setup_progress');
        if (savedProgress) {
          const { steps } = JSON.parse(savedProgress);

          // Update steps state
          const updatedSteps = steps.map((step: any) =>
            step.id === stepId ? { ...step, completed: true } : step
          );

          // Calculate next step for navigation
          const completedStepIndex = updatedSteps.findIndex((step: any) => step.id === stepId);
          let nextStepIndex = completedStepIndex;
          if (completedStepIndex === updatedSteps.length - 1) {
            nextStepIndex = updatedSteps.length; // Beyond last step = completion
            console.log(`🎉 Final step completed! Ready for completion.`);
          } else {
            // Find next incomplete step
            const nextIncompleteIndex = updatedSteps.findIndex(
              (step: any, index: number) => index > completedStepIndex && !step.completed
            );
            nextStepIndex = nextIncompleteIndex >= 0 ? nextIncompleteIndex : completedStepIndex + 1;
            console.log(`🚀 Auto-advancing from step ${completedStepIndex} to step ${nextStepIndex}`);
          }

          // Save updated progress
          await AsyncStorage.setItem(
            'health_profile_setup_progress',
            JSON.stringify({
              currentStep: nextStepIndex,
              steps: updatedSteps,
            })
          );
          console.log(`💾 Progress updated: step ${nextStepIndex}, ${updatedSteps.filter((s: any) => s.completed).length}/${updatedSteps.length} completed`);
        }

        // 3. Auto-save to health profile ONLY if data provided
        if (stepData) {
          const mappedKey = sectionKeyMap[stepId];
          if (mappedKey) {
            try {
              const result = await updateProfile(mappedKey as any, stepData);
              if (result && result.error) {
                console.error(`❌ Error saving ${stepId}:`, result.error);
                Alert.alert(
                  'Save Error',
                  `There was an issue saving your ${mappedKey.replace(/([A-Z])/g, ' $1').toLowerCase()}. Please try again.`
                );
                return;
              }
              console.log(`✅ Successfully saved ${stepId} to health profile`);
            } catch (error) {
              console.error(`❌ Failed to save ${stepId}:`, error);
              Alert.alert(
                'Save Error',
                `There was an issue saving your ${mappedKey.replace(/([A-Z])/g, ' $1').toLowerCase()}. Please try again.`
              );
              return;
            }
          }
        }

        // 4. Always return to main setup screen after saving
        console.log(`🔄 Returning to HealthProfileSetup screen`);
        // Use goBack() instead of navigate to preserve setup screen state
        navigation.goBack();
      } catch (error) {
        console.error(`❌ Error in sharedStepCompletion for ${stepId}:`, error);
        Alert.alert('Error', 'There was an issue saving your data. Please try again.');
      }
    },
    [navigation, updateProfile]
  );

  return { sharedStepCompletion };
}
