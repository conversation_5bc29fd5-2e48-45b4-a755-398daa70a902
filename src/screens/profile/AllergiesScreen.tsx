// src/screens/profile/AllergiesScreen.tsx
// 🚀 WORLD-CLASS: Allergies & Sensitivities Selection Screen
import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  TouchableOpacity,
  ScrollView,
  Alert,
  TextInput,
} from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { OptimizedIcon } from '../../components/common/OptimizedIcon';
import { COLORS, SPACING, TYPOGRAPHY } from '../../constants';
import { AllergiesScreenProps } from '../../types/navigation';
import { useHealthProfile } from '../../hooks/useHealthProfile';
import { useHealthProfileStepCompletion } from '../../hooks/useHealthProfileStepCompletion';
import type {
  AllergiesAndSensitivities,
  Allergy as AllergyType,
} from '../../types/healthProfile';

interface AllergyOption {
  id: string;
  name: string;
  category: 'food' | 'environmental' | 'medication' | 'supplement';
  severity: 'mild' | 'moderate' | 'severe';
  icon: string; // Use string for compatibility with OptimizedIcon
}

const COMMON_ALLERGIES: AllergyOption[] = [
  // Food Allergies
  {
    id: 'shellfish',
    name: 'Shellfish',
    category: 'food',
    severity: 'severe',
    icon: 'set-meal',
  },
  {
    id: 'nuts',
    name: 'Tree Nuts',
    category: 'food',
    severity: 'severe',
    icon: 'eco',
  },
  {
    id: 'peanuts',
    name: 'Peanuts',
    category: 'food',
    severity: 'severe',
    icon: 'eco',
  },
  {
    id: 'dairy',
    name: 'Dairy/Lactose',
    category: 'food',
    severity: 'moderate',
    icon: 'local-drink',
  },
  {
    id: 'gluten',
    name: 'Gluten',
    category: 'food',
    severity: 'moderate',
    icon: 'grain',
  },
  {
    id: 'soy',
    name: 'Soy',
    category: 'food',
    severity: 'moderate',
    icon: 'eco',
  },
  {
    id: 'eggs',
    name: 'Eggs',
    category: 'food',
    severity: 'moderate',
    icon: 'egg',
  },

  // Medication Allergies
  {
    id: 'penicillin',
    name: 'Penicillin',
    category: 'medication',
    severity: 'severe',
    icon: 'medication',
  },
  {
    id: 'aspirin',
    name: 'Aspirin/NSAIDs',
    category: 'medication',
    severity: 'moderate',
    icon: 'medication',
  },
  {
    id: 'sulfa',
    name: 'Sulfa Drugs',
    category: 'medication',
    severity: 'moderate',
    icon: 'medication',
  },

  // Supplement Ingredients
  {
    id: 'gelatin',
    name: 'Gelatin',
    category: 'supplement',
    severity: 'mild',
    icon: 'science',
  },
  {
    id: 'artificial_colors',
    name: 'Artificial Colors',
    category: 'supplement',
    severity: 'mild',
    icon: 'palette',
  },
  {
    id: 'titanium_dioxide',
    name: 'Titanium Dioxide',
    category: 'supplement',
    severity: 'mild',
    icon: 'science',
  },

  // Environmental
  {
    id: 'latex',
    name: 'Latex',
    category: 'environmental',
    severity: 'moderate',
    icon: 'healing',
  },
];

const SEVERITY_COLORS = {
  mild: COLORS.warning,
  moderate: COLORS.error,
  severe: '#D32F2F',
};

export const AllergiesScreen: React.FC<AllergiesScreenProps> = ({
  navigation,
  route,
}) => {
  // Accept initialValue from navigation params for progress recovery
  const initialValue = route?.params?.initialValue as
    | {
        selectedAllergies?: string[];
        customAllergies?: string[];
        allAllergies?: string[];
      }
    | undefined;
  const [selectedAllergies, setSelectedAllergies] = useState<string[]>(
    initialValue?.selectedAllergies || []
  );
  const [customAllergy, setCustomAllergy] = useState('');
  const [customAllergies, setCustomAllergies] = useState<string[]>(
    initialValue?.customAllergies || []
  );
  const [isLoading, setIsLoading] = useState(false);
  const { updateProfile } = useHealthProfile();
  const { sharedStepCompletion } = useHealthProfileStepCompletion();

  // 💾 Load existing data when component mounts (if not provided by initialValue)
  useEffect(() => {
    if (
      initialValue &&
      (initialValue.selectedAllergies?.length ||
        initialValue.customAllergies?.length)
    )
      return;
    const loadExistingData = async () => {
      try {
        // Load from setup data (temporary storage during setup flow)
        const savedSetupData = await AsyncStorage.getItem(
          'health_profile_setup_data'
        );
        if (savedSetupData) {
          const setupData = JSON.parse(savedSetupData);
          if (setupData.allergies) {
            const { selectedAllergies: saved, customAllergies: savedCustom } =
              setupData.allergies;
            if (Array.isArray(saved)) {
              setSelectedAllergies(saved);
            }
            if (Array.isArray(savedCustom)) {
              setCustomAllergies(savedCustom);
            }
          }
        }
      } catch (error) {
        console.error('Error loading existing allergies:', error);
      }
    };

    loadExistingData();
  }, [initialValue]);

  // 🔄 UNIFIED: Use shared step completion logic (removed local markStepComplete)

  const handleAllergyToggle = (allergyId: string) => {
    setSelectedAllergies(prev => {
      if (prev.includes(allergyId)) {
        return prev.filter(id => id !== allergyId);
      } else {
        return [...prev, allergyId];
      }
    });
  };

  const handleAddCustomAllergy = () => {
    if (
      customAllergy.trim() &&
      !customAllergies.includes(customAllergy.trim())
    ) {
      setCustomAllergies(prev => [...prev, customAllergy.trim()]);
      setCustomAllergy('');
    }
  };

  const handleRemoveCustomAllergy = (allergy: string) => {
    setCustomAllergies(prev => prev.filter(a => a !== allergy));
  };

  const handleSave = async () => {
    const allAllergies = [...selectedAllergies, ...customAllergies];

    setIsLoading(true);
    try {
      console.log('💾 Saving allergies:', {
        selectedAllergies,
        customAllergies,
      });

      // 1. Save to setupData (temporary storage during setup flow)
      const savedSetupData = await AsyncStorage.getItem(
        'health_profile_setup_data'
      );
      const setupData = savedSetupData ? JSON.parse(savedSetupData) : {};
      setupData.allergies = {
        selectedAllergies,
        customAllergies,
        allAllergies,
      };
      await AsyncStorage.setItem(
        'health_profile_setup_data',
        JSON.stringify(setupData)
      );

      // 2. Transform to AllergiesAndSensitivities interface and save to health profile
      const allergiesData: AllergiesAndSensitivities = {
        allergies: allAllergies.map(allergy => ({
          id: typeof allergy === 'string' ? allergy : allergy,
          name: typeof allergy === 'string' ? allergy : allergy,
          type: 'other' as const,
          severity: 'moderate' as const,
          reaction: customAllergies.includes(allergy)
            ? 'Custom allergy'
            : undefined,
          confirmed: true,
        })),
        consentGiven: true,
        lastUpdated: new Date().toISOString(),
      };

      const result = await updateProfile('allergies', allergiesData);
      if (result.error) {
        throw new Error(
          typeof result.error === 'string'
            ? result.error
            : 'Failed to save allergies'
        );
      }

      // 3. Mark allergies step as complete and always return to setup screen
      await sharedStepCompletion('allergies', allergiesData);
      console.log('✅ Successfully saved allergies');
    } catch (error) {
      console.error('❌ Error saving allergies:', error);
      Alert.alert('Error', 'Failed to save allergies. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const renderAllergyCard = (allergy: AllergyOption) => {
    const isSelected = selectedAllergies.includes(allergy.id);
    const severityColor = SEVERITY_COLORS[allergy.severity];

    return (
      <TouchableOpacity
        key={allergy.id}
        style={[styles.allergyCard, isSelected && styles.selectedAllergyCard]}
        onPress={() => handleAllergyToggle(allergy.id)}
        activeOpacity={0.7}
      >
        <View style={styles.allergyHeader}>
          <View
            style={[
              styles.allergyIconContainer,
              isSelected && styles.selectedIconContainer,
            ]}
          >
            <OptimizedIcon
              type="material"
              name={allergy.icon}
              size={20}
              color={isSelected ? COLORS.white : severityColor}
            />
          </View>
          <View style={styles.allergyInfo}>
            <Text
              style={[
                styles.allergyName,
                isSelected && styles.selectedAllergyName,
              ]}
            >
              {allergy.name}
            </Text>
            <View style={styles.severityContainer}>
              <View
                style={[styles.severityDot, { backgroundColor: severityColor }]}
              />
              <Text
                style={[
                  styles.severityText,
                  isSelected && styles.selectedSeverityText,
                ]}
              >
                {allergy.severity.charAt(0).toUpperCase() +
                  allergy.severity.slice(1)}
              </Text>
            </View>
          </View>
          {isSelected && (
            <OptimizedIcon
              type="ion"
              name="checkmark-circle"
              size={20}
              color={COLORS.success}
              accessibilityLabel="Selected allergy"
            />
          )}
        </View>
      </TouchableOpacity>
    );
  };

  const groupedAllergies = COMMON_ALLERGIES.reduce(
    (acc, allergy) => {
      if (!acc[allergy.category]) {
        acc[allergy.category] = [];
      }
      acc[allergy.category].push(allergy);
      return acc;
    },
    {} as Record<string, AllergyOption[]>
  );

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity
          onPress={() => navigation.goBack()}
          style={styles.backButton}
        >
          <OptimizedIcon
            type="ion"
            name="arrow-back"
            size={24}
            color={COLORS.textPrimary}
          />
        </TouchableOpacity>
        <Text style={styles.title}>Allergies</Text>
        <TouchableOpacity onPress={() => navigation.goBack()}>
          <Text style={styles.skipText}>Skip</Text>
        </TouchableOpacity>
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Instructions */}
        <View style={styles.instructions}>
          <OptimizedIcon
            type="ion"
            name="warning"
            size={32}
            color={COLORS.error}
          />
          <Text style={styles.instructionsTitle}>
            Allergies & Sensitivities
          </Text>
          <Text style={styles.instructionsText}>
            Select any allergies or sensitivities you have. This is critical for
            your safety and helps us avoid recommending harmful supplements.
          </Text>
          <View style={styles.criticalNote}>
            <OptimizedIcon
              type="ion"
              name="alert-circle"
              size={16}
              color={COLORS.error}
            />
            <Text style={styles.criticalText}>
              Critical safety information - please be thorough
            </Text>
          </View>
        </View>

        {/* Grouped Allergies */}
        {Object.entries(groupedAllergies).map(([category, allergies]) => (
          <View key={category} style={styles.section}>
            <Text style={styles.sectionTitle}>
              {category.charAt(0).toUpperCase() + category.slice(1)} Allergies
            </Text>
            <View style={styles.allergiesGrid}>
              {allergies.map(renderAllergyCard)}
            </View>
          </View>
        ))}

        {/* Custom Allergies */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Add Custom Allergy</Text>
          <View style={styles.customInputContainer}>
            <TextInput
              style={styles.customInput}
              placeholder="Enter allergy or sensitivity..."
              value={customAllergy}
              onChangeText={setCustomAllergy}
              onSubmitEditing={handleAddCustomAllergy}
              returnKeyType="done"
            />
            <TouchableOpacity
              style={styles.addButton}
              onPress={handleAddCustomAllergy}
              disabled={!customAllergy.trim()}
            >
              <OptimizedIcon
                type="ion"
                name="add"
                size={20}
                color={COLORS.white}
              />
            </TouchableOpacity>
          </View>

          {/* Custom Allergies List */}
          {customAllergies.map((allergy, index) => (
            <View key={index} style={styles.customAllergyItem}>
              <Text style={styles.customAllergyText}>{allergy}</Text>
              <TouchableOpacity
                onPress={() => handleRemoveCustomAllergy(allergy)}
                style={styles.removeButton}
              >
                <OptimizedIcon
                  type="ion"
                  name="close"
                  size={16}
                  color={COLORS.error}
                />
              </TouchableOpacity>
            </View>
          ))}
        </View>
      </ScrollView>

      {/* Save Button */}
      <View style={styles.footer}>
        <TouchableOpacity
          style={[styles.saveButton, isLoading && styles.saveButtonDisabled]}
          onPress={handleSave}
          disabled={isLoading}
        >
          <Text style={styles.saveButtonText}>
            {isLoading ? 'Saving...' : 'Save Allergies'}
          </Text>
          {!isLoading && (
            <OptimizedIcon
              type="ion"
              name="arrow-forward"
              size={20}
              color={COLORS.white}
            />
          )}
        </TouchableOpacity>
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.background,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: SPACING.lg,
    paddingVertical: SPACING.md,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.gray200,
  },
  backButton: {
    padding: SPACING.sm,
  },
  title: {
    fontSize: TYPOGRAPHY.sizes.xl,
    fontWeight: TYPOGRAPHY.weights.bold,
    color: COLORS.textPrimary,
  },
  skipText: {
    fontSize: TYPOGRAPHY.sizes.base,
    color: COLORS.primary,
    fontWeight: TYPOGRAPHY.weights.medium,
  },
  content: {
    flex: 1,
  },
  instructions: {
    alignItems: 'center',
    paddingHorizontal: SPACING.lg,
    paddingVertical: SPACING.xl,
  },
  instructionsTitle: {
    fontSize: TYPOGRAPHY.sizes.xl,
    fontWeight: TYPOGRAPHY.weights.bold,
    color: COLORS.textPrimary,
    marginTop: SPACING.md,
    marginBottom: SPACING.sm,
  },
  instructionsText: {
    fontSize: TYPOGRAPHY.sizes.base,
    color: COLORS.textSecondary,
    textAlign: 'center',
    lineHeight: 22,
    marginBottom: SPACING.md,
  },
  criticalNote: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: COLORS.errorLight,
    paddingHorizontal: SPACING.md,
    paddingVertical: SPACING.sm,
    borderRadius: 8,
    gap: SPACING.xs,
  },
  criticalText: {
    fontSize: TYPOGRAPHY.sizes.sm,
    color: COLORS.error,
    fontWeight: TYPOGRAPHY.weights.semibold,
  },
  section: {
    paddingHorizontal: SPACING.lg,
    marginBottom: SPACING.xl,
  },
  sectionTitle: {
    fontSize: TYPOGRAPHY.sizes.lg,
    fontWeight: TYPOGRAPHY.weights.semibold,
    color: COLORS.textPrimary,
    marginBottom: SPACING.md,
  },
  allergiesGrid: {
    gap: SPACING.sm,
  },
  allergyCard: {
    backgroundColor: COLORS.backgroundSecondary,
    borderRadius: 12,
    padding: SPACING.md,
    borderWidth: 1,
    borderColor: COLORS.gray200,
  },
  selectedAllergyCard: {
    backgroundColor: COLORS.errorLight,
    borderColor: COLORS.error,
  },
  allergyHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: SPACING.md,
  },
  allergyIconContainer: {
    width: 32,
    height: 32,
    borderRadius: 8,
    backgroundColor: COLORS.errorLight,
    alignItems: 'center',
    justifyContent: 'center',
  },
  selectedIconContainer: {
    backgroundColor: COLORS.error,
  },
  allergyInfo: {
    flex: 1,
  },
  allergyName: {
    fontSize: TYPOGRAPHY.sizes.base,
    fontWeight: TYPOGRAPHY.weights.semibold,
    color: COLORS.textPrimary,
    marginBottom: SPACING.xs,
  },
  selectedAllergyName: {
    color: COLORS.error,
  },
  severityContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: SPACING.xs,
  },
  severityDot: {
    width: 6,
    height: 6,
    borderRadius: 3,
  },
  severityText: {
    fontSize: TYPOGRAPHY.sizes.sm,
    color: COLORS.textSecondary,
    fontWeight: TYPOGRAPHY.weights.medium,
  },
  selectedSeverityText: {
    color: COLORS.error + 'CC',
  },
  customInputContainer: {
    flexDirection: 'row',
    gap: SPACING.sm,
    marginBottom: SPACING.md,
  },
  customInput: {
    flex: 1,
    backgroundColor: COLORS.backgroundSecondary,
    borderRadius: 8,
    paddingHorizontal: SPACING.md,
    paddingVertical: SPACING.sm,
    fontSize: TYPOGRAPHY.sizes.base,
    color: COLORS.textPrimary,
    borderWidth: 1,
    borderColor: COLORS.gray200,
  },
  addButton: {
    backgroundColor: COLORS.primary,
    borderRadius: 8,
    paddingHorizontal: SPACING.md,
    paddingVertical: SPACING.sm,
    alignItems: 'center',
    justifyContent: 'center',
  },
  customAllergyItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: COLORS.backgroundSecondary,
    borderRadius: 8,
    paddingHorizontal: SPACING.md,
    paddingVertical: SPACING.sm,
    marginBottom: SPACING.sm,
  },
  customAllergyText: {
    fontSize: TYPOGRAPHY.sizes.base,
    color: COLORS.textPrimary,
    flex: 1,
  },
  removeButton: {
    padding: SPACING.xs,
  },
  footer: {
    padding: SPACING.lg,
    borderTopWidth: 1,
    borderTopColor: COLORS.gray200,
  },
  saveButton: {
    backgroundColor: COLORS.success,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: SPACING.md,
    borderRadius: 12,
    gap: SPACING.sm,
  },
  saveButtonDisabled: {
    backgroundColor: COLORS.gray400,
    opacity: 0.6,
  },
  saveButtonText: {
    fontSize: TYPOGRAPHY.sizes.lg,
    fontWeight: TYPOGRAPHY.weights.semibold,
    color: COLORS.white,
  },
});
