// src/services/products/productService.ts

import { Product, ProductAnalysis, UserStack } from '@/types';
import { openFoodFactsService } from './openfoodfacts';
import { scanService } from '@/services/database';
import { convertSearchResultToProduct } from '../search/productConverter';

class ProductService {
  /**
   * Analyze a scanned product with the user's stack
   */
  async analyzeScannedProduct(
    barcode: string,
    userStack: UserStack[]
  ): Promise<{ product: Product; analysis: ProductAnalysis }> {
    try {
      // Get product (from DB or OpenFoodFacts)
      const parsedProduct =
        await openFoodFactsService.getProductByBarcode(barcode);

      if (!parsedProduct) {
        throw new Error('Product not found');
      }

      // Convert to Product format
      const product: Product = {
        id: parsedProduct.barcode || `product_${Date.now()}`,
        name: parsedProduct.name,
        brand: parsedProduct.brand || '',
        category: 'specialty' as const,
        barcode: parsedProduct.barcode,
        ingredients: (parsedProduct.ingredients || []).map((ing: any) => ({
          name: typeof ing === 'string' ? ing : ing?.name || 'Unknown',
          amount: 0,
          unit: 'mg',
          form: 'other' as const,
          bioavailability: 'medium' as const,
          evidenceLevel: 'observational' as const,
          category: 'active' as const,
        })),
        servingSize: '1 serving',
        servingsPerContainer: 1,
        imageUrl: parsedProduct.imageUrl,
        verified: parsedProduct.found,
        thirdPartyTested: false,
        certifications: [],
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };

      // Analyze with user's stack
      const analysis = await this.analyzeProduct(product, userStack);

      return { product, analysis };
    } catch (error) {
      console.error('Error analyzing scanned product:', error);
      throw error;
    }
  }

  /**
   * Analyze a product against the user's stack
   */
  async analyzeProduct(
    product: Product,
    _userStack: UserStack[]
  ): Promise<ProductAnalysis> {
    // Simple interaction check (placeholder)
    const interactions = {
      interactions: [],
      nutrientWarnings: [],
      overallRiskLevel: 'NONE' as const,
    };

    // Calculate scores
    const safetyScore = this.calculateSafetyScore(interactions);
    const efficacyScore = this.calculateEfficacyScore(product);
    const valueScore = this.calculateValueScore(product);

    return {
      overallScore: Math.round((safetyScore + efficacyScore + valueScore) / 3),
      categoryScores: {
        ingredients: safetyScore,
        bioavailability: efficacyScore,
        dosage: 75,
        purity: 80,
        value: valueScore,
      },
      strengths: [
        {
          point: 'Quality ingredients',
          detail: 'Product contains verified ingredients',
          importance: 'medium' as const,
          category: 'quality' as const,
        },
      ],
      weaknesses: [],
      recommendations: {
        goodFor: ['General health support'],
        avoidIf: [],
      },
      aiReasoning: 'Basic analysis completed successfully',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };
  }

  private calculateSafetyScore(interactions: any): number {
    if (interactions.overallRiskLevel === 'CRITICAL') return 20;
    if (interactions.overallRiskLevel === 'HIGH') return 40;
    if (interactions.overallRiskLevel === 'MODERATE') return 60;
    if (interactions.overallRiskLevel === 'LOW') return 80;
    return 100;
  }

  private calculateEfficacyScore(product: Product): number {
    // Basic scoring - enhance this with real logic
    let score = 50;

    // Bonus for verified products
    if (product.verified) score += 20;

    // Bonus for third-party testing
    if (product.thirdPartyTested) score += 20;

    // Bonus for certifications
    score += Math.min(product.certifications.length * 5, 10);

    return Math.min(score, 100);
  }

  private calculateValueScore(_product: Product): number {
    // Placeholder - implement real value scoring
    return 70;
  }

  // Note: generateRecommendations method removed as it's not used

  /**
   * Analyze a search result product with the user's stack
   */
  async analyzeSearchResult(
    searchResult: any,
    userStack: UserStack[]
  ): Promise<{ product: Product; analysis: ProductAnalysis }> {
    try {
      // Convert search result to Product format
      const product = convertSearchResultToProduct(searchResult);

      // Analyze with user's stack using the same pipeline as scanned products
      const analysis = await this.analyzeProduct(product, userStack);

      return { product, analysis };
    } catch (error) {
      console.error('Error analyzing search result:', error);
      throw error;
    }
  }

  /**
   * Record scan in history
   */
  async recordScan(
    userId: string | null,
    product: Product,
    scanType: string,
    analysisScore: number
  ) {
    return scanService.recordScan(userId, product.id, scanType, analysisScore);
  }
}

export const productService = new ProductService();
