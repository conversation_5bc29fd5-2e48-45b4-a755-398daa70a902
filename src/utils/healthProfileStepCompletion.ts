/**
 * 🔄 UNIFIED HEALTH PROFILE STEP COMPLETION UTILITY
 * 
 * This utility provides a centralized way to handle step completion
 * across all health profile setup screens, ensuring consistent
 * navigation and data persistence.
 */

import AsyncStorage from '@react-native-async-storage/async-storage';

export interface StepCompletionData {
  stepId: string;
  stepData: any;
  isLastStep?: boolean;
}

/**
 * 🔄 UNIFIED: Complete a health profile setup step
 * This function handles:
 * 1. Marking the step as complete in storage
 * 2. Calculating the next step for navigation
 * 3. Saving progress to AsyncStorage
 * 4. Returning navigation information
 */
export const completeHealthProfileStep = async ({
  stepId,
  stepData,
  isLastStep = false,
}: StepCompletionData): Promise<{
  success: boolean;
  nextStep: number;
  shouldNavigateToSetup: boolean;
  error?: string;
}> => {
  try {
    console.log(`🔄 Completing step: ${stepId}`, isLastStep ? '(FINAL STEP)' : '');

    // 1. Load current progress
    const savedProgress = await AsyncStorage.getItem('health_profile_setup_progress');
    if (!savedProgress) {
      throw new Error('No setup progress found');
    }

    const { steps, currentStep } = JSON.parse(savedProgress);

    // 2. Update the completed step
    const updatedSteps = steps.map((step: any) =>
      step.id === stepId ? { ...step, completed: true } : step
    );

    // 3. Calculate next step
    const completedStepIndex = steps.findIndex((step: any) => step.id === stepId);
    let nextStep: number;
    let shouldNavigateToSetup = false;

    if (isLastStep || completedStepIndex === steps.length - 1) {
      // Final step - advance beyond to show completion
      nextStep = steps.length;
      shouldNavigateToSetup = true;
      console.log('🎉 Final step completed! Ready for completion.');
    } else {
      // Find next incomplete step
      const nextIncompleteIndex = updatedSteps.findIndex(
        (step: any, index: number) => index > completedStepIndex && !step.completed
      );
      nextStep = nextIncompleteIndex >= 0 ? nextIncompleteIndex : completedStepIndex + 1;
      shouldNavigateToSetup = false;
      console.log(`🚀 Auto-advancing from step ${completedStepIndex} to step ${nextStep}`);
    }

    // 4. Save updated progress
    await AsyncStorage.setItem(
      'health_profile_setup_progress',
      JSON.stringify({
        currentStep: nextStep,
        steps: updatedSteps,
      })
    );

    // 5. Save step data
    const savedData = await AsyncStorage.getItem('health_profile_setup_data');
    const setupData = savedData ? JSON.parse(savedData) : {};
    setupData[stepId] = stepData;
    
    await AsyncStorage.setItem(
      'health_profile_setup_data',
      JSON.stringify(setupData)
    );

    console.log(`✅ Step ${stepId} completed successfully. Next step: ${nextStep}`);

    return {
      success: true,
      nextStep,
      shouldNavigateToSetup,
    };
  } catch (error) {
    console.error(`❌ Error completing step ${stepId}:`, error);
    return {
      success: false,
      nextStep: 0,
      shouldNavigateToSetup: false,
      error: error instanceof Error ? error.message : 'Unknown error',
    };
  }
};

/**
 * 🔍 Get the next screen name for navigation
 */
export const getNextScreenName = (nextStep: number): string => {
  const stepScreenMap: Record<number, string> = {
    1: 'Demographics',
    2: 'HealthGoals', 
    3: 'HealthConditions',
    4: 'Allergies',
  };
  
  return stepScreenMap[nextStep] || 'HealthProfileSetup';
};

/**
 * 🧹 Clear all health profile setup progress (for testing/reset)
 */
export const clearHealthProfileProgress = async (): Promise<void> => {
  try {
    await Promise.all([
      AsyncStorage.removeItem('health_profile_setup_progress'),
      AsyncStorage.removeItem('health_profile_setup_data'),
      AsyncStorage.removeItem('health_profile_consents'),
    ]);
    console.log('🧹 Health profile progress cleared');
  } catch (error) {
    console.error('❌ Error clearing progress:', error);
  }
};

// 🧪 TESTING: Call this to reset progress for testing
// clearHealthProfileProgress();
