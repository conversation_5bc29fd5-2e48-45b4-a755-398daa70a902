-- =====================================================
-- PHARMAGUIDE DATABASE ENHANCEMENTS
-- Run this AFTER the main migration (paste-5.txt)
-- =====================================================

-- 1. Add exact match optimization for common queries
CREATE INDEX idx_critical_rules_exact 
ON critical_interaction_rules(LOWER(item1_identifier), LOWER(item2_identifier)) 
WHERE active = TRUE;

-- Also create reverse index for bidirectional lookups
CREATE INDEX idx_critical_rules_exact_reverse 
ON critical_interaction_rules(LOWER(item2_identifier), LOWER(item1_identifier)) 
WHERE active = TRUE;

-- 2. Create periodic cleanup function for AI cache
CREATE OR REPLACE FUNCTION cleanup_expired_ai_cache() 
RETURNS integer AS $$
DECLARE
    deleted_count integer;
BEGIN
    DELETE FROM ai_response_cache 
    WHERE expires_at < NOW() - INTERVAL '7 days';
    
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    
    -- Log the cleanup
    INSERT INTO audit_log (table_name, record_id, operation, new_data)
    VALUES ('ai_response_cache', gen_random_uuid(), 'CLEANUP', 
            jsonb_build_object('deleted_count', deleted_count, 'cleanup_date', NOW()));
    
    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Schedule this as a Supabase Edge Function cron job
COMMENT ON FUNCTION cleanup_expired_ai_cache IS 'Run weekly via Edge Function: select cleanup_expired_ai_cache();';

-- 3. Add versioning support for interaction rules
ALTER TABLE critical_interaction_rules 
ADD COLUMN IF NOT EXISTS superseded_by uuid REFERENCES critical_interaction_rules(id),
ADD COLUMN IF NOT EXISTS version_notes text;

-- Create index for version tracking
CREATE INDEX idx_critical_rules_superseded 
ON critical_interaction_rules(superseded_by) 
WHERE superseded_by IS NOT NULL;

-- 4. Link feedback to actual fixes
ALTER TABLE feedback_reports 
ADD COLUMN IF NOT EXISTS resulted_in_rule_id uuid REFERENCES critical_interaction_rules(id),
ADD COLUMN IF NOT EXISTS implemented_at timestamptz,
ADD COLUMN IF NOT EXISTS implementation_notes text;

-- Create index for tracking implemented feedback
CREATE INDEX idx_feedback_implemented 
ON feedback_reports(resulted_in_rule_id) 
WHERE resulted_in_rule_id IS NOT NULL;

-- 5. Add provider verification fields for B2B
ALTER TABLE user_roles 
ADD COLUMN IF NOT EXISTS verification_document_url text,
ADD COLUMN IF NOT EXISTS verification_status varchar(50) DEFAULT 'PENDING' 
    CHECK (verification_status IN ('PENDING', 'VERIFIED', 'REJECTED', 'EXPIRED')),
ADD COLUMN IF NOT EXISTS verified_at timestamptz,
ADD COLUMN IF NOT EXISTS verification_expires_at timestamptz,
ADD COLUMN IF NOT EXISTS license_number varchar(100),
ADD COLUMN IF NOT EXISTS license_state varchar(2);

-- Create index for verified providers
CREATE INDEX idx_user_roles_verified_providers 
ON user_roles(role, verification_status) 
WHERE role = 'provider' AND verification_status = 'VERIFIED';

-- 6. Add cache warming function for common interactions
CREATE OR REPLACE FUNCTION warm_interaction_cache() 
RETURNS void AS $$
DECLARE
    common_pair RECORD;
BEGIN
    -- Find most commonly checked interaction pairs from logs
    FOR common_pair IN 
        SELECT DISTINCT 
            (items_checked->0->>'name') as item1,
            (items_checked->1->>'name') as item2,
            COUNT(*) as check_count
        FROM interaction_check_logs
        WHERE jsonb_array_length(items_checked) >= 2
        GROUP BY 1, 2
        ORDER BY check_count DESC
        LIMIT 100
    LOOP
        -- Trigger cache warming by calling get_interaction_tier
        PERFORM get_interaction_tier(
            'supplement', common_pair.item1,
            'supplement', common_pair.item2
        );
    END LOOP;
END;
$$ LANGUAGE plpgsql;

-- 7. Add analytics rollup function
CREATE OR REPLACE FUNCTION rollup_daily_analytics() 
RETURNS void AS $$
BEGIN
    -- Archive old detailed logs (older than 30 days)
    INSERT INTO interaction_check_logs_archive 
    SELECT * FROM interaction_check_logs 
    WHERE checked_at < NOW() - INTERVAL '30 days';
    
    -- Delete archived logs from main table
    DELETE FROM interaction_check_logs 
    WHERE checked_at < NOW() - INTERVAL '30 days';
    
    -- Update materialized view for faster analytics
    REFRESH MATERIALIZED VIEW CONCURRENTLY interaction_statistics_daily;
END;
$$ LANGUAGE plpgsql;

-- 8. Create archive table for old logs
CREATE TABLE IF NOT EXISTS interaction_check_logs_archive (
    LIKE interaction_check_logs INCLUDING ALL
);

-- 9. Create materialized view for analytics
CREATE MATERIALIZED VIEW IF NOT EXISTS interaction_statistics_daily AS
SELECT
    DATE_TRUNC('day', checked_at) as check_date,
    COUNT(*) as total_checks,
    COUNT(DISTINCT session_id) as unique_sessions,
    AVG(interaction_count) as avg_interactions_per_check,
    SUM(critical_count) as total_critical,
    SUM(high_count) as total_high,
    AVG(response_time_ms) as avg_response_time,
    COUNT(CASE WHEN tier_used = 'RULES' THEN 1 END) as rules_count,
    COUNT(CASE WHEN tier_used = 'CACHE' THEN 1 END) as cache_count,
    COUNT(CASE WHEN tier_used = 'AI' THEN 1 END) as ai_count,
    (COUNT(CASE WHEN tier_used = 'CACHE' THEN 1 END)::float / NULLIF(COUNT(*), 0)) as cache_hit_rate
FROM interaction_check_logs
GROUP BY DATE_TRUNC('day', checked_at);

-- Create index on materialized view
CREATE INDEX idx_interaction_stats_daily_date 
ON interaction_statistics_daily(check_date DESC);

-- 10. Add function to track AI model performance over time
CREATE OR REPLACE FUNCTION update_ai_model_performance(
    p_model_name varchar,
    p_provider varchar,
    p_success boolean,
    p_response_time_ms integer,
    p_tokens integer DEFAULT 0,
    p_cost decimal DEFAULT 0,
    p_quality_score decimal DEFAULT NULL
) RETURNS void AS $$
BEGIN
    INSERT INTO ai_model_metrics (
        model_name, provider, request_count, success_count, 
        error_count, total_tokens, total_cost, last_used
    ) VALUES (
        p_model_name, p_provider, 1, 
        CASE WHEN p_success THEN 1 ELSE 0 END,
        CASE WHEN NOT p_success THEN 1 ELSE 0 END,
        p_tokens, p_cost, NOW()
    )
    ON CONFLICT (model_name, provider) 
    DO UPDATE SET
        request_count = ai_model_metrics.request_count + 1,
        success_count = ai_model_metrics.success_count + CASE WHEN p_success THEN 1 ELSE 0 END,
        error_count = ai_model_metrics.error_count + CASE WHEN NOT p_success THEN 1 ELSE 0 END,
        total_tokens = ai_model_metrics.total_tokens + EXCLUDED.total_tokens,
        total_cost = ai_model_metrics.total_cost + EXCLUDED.total_cost,
        avg_response_time_ms = (
            (COALESCE(ai_model_metrics.avg_response_time_ms, 0) * ai_model_metrics.request_count + p_response_time_ms) 
            / (ai_model_metrics.request_count + 1)
        )::integer,
        avg_quality_score = CASE 
            WHEN p_quality_score IS NOT NULL THEN
                (
                    (COALESCE(ai_model_metrics.avg_quality_score, 0) * ai_model_metrics.request_count + p_quality_score) 
                    / (ai_model_metrics.request_count + 1)
                )::decimal(3,2)
            ELSE ai_model_metrics.avg_quality_score
        END,
        error_rate = (ai_model_metrics.error_count + CASE WHEN NOT p_success THEN 1 ELSE 0 END)::decimal / (ai_model_metrics.request_count + 1),
        last_used = NOW(),
        updated_at = NOW();
END;
$$ LANGUAGE plpgsql;

-- 11. Add unique constraint to ai_model_metrics
ALTER TABLE ai_model_metrics 
ADD CONSTRAINT unique_model_provider UNIQUE (model_name, provider);

-- 12. Grant permissions for new functions
GRANT EXECUTE ON FUNCTION cleanup_expired_ai_cache TO authenticated;
GRANT EXECUTE ON FUNCTION warm_interaction_cache TO authenticated;
GRANT EXECUTE ON FUNCTION update_ai_model_performance TO authenticated;

-- 13. Create helper function for interaction normalization
CREATE OR REPLACE FUNCTION normalize_interaction_check(
    p_item1_name varchar,
    p_item2_name varchar
) RETURNS TABLE (
    normalized_item1 varchar,
    normalized_item2 varchar,
    item1_type varchar,
    item2_type varchar
) AS $$
BEGIN
    RETURN QUERY
    WITH normalized AS (
        SELECT 
            COALESCE(sm1.generic_name, LOWER(TRIM(p_item1_name))) as norm_item1,
            COALESCE(sm2.generic_name, LOWER(TRIM(p_item2_name))) as norm_item2,
            COALESCE(sm1.category, 'unknown') as cat1,
            COALESCE(sm2.category, 'unknown') as cat2
        FROM 
            (SELECT 1) dummy
        LEFT JOIN substance_mappings sm1 
            ON p_item1_name ILIKE ANY(sm1.aliases) 
            OR LOWER(p_item1_name) = LOWER(sm1.common_name)
        LEFT JOIN substance_mappings sm2 
            ON p_item2_name ILIKE ANY(sm2.aliases) 
            OR LOWER(p_item2_name) = LOWER(sm2.common_name)
    )
    SELECT 
        CASE WHEN norm_item1 < norm_item2 THEN norm_item1 ELSE norm_item2 END,
        CASE WHEN norm_item1 < norm_item2 THEN norm_item2 ELSE norm_item1 END,
        CASE WHEN norm_item1 < norm_item2 THEN cat1 ELSE cat2 END,
        CASE WHEN norm_item1 < norm_item2 THEN cat2 ELSE cat1 END
    FROM normalized;
END;
$$ LANGUAGE plpgsql;

-- 14. Add comments for documentation
COMMENT ON FUNCTION cleanup_expired_ai_cache IS 'Removes AI cache entries older than 7 days. Schedule weekly.';
COMMENT ON FUNCTION warm_interaction_cache IS 'Pre-populates cache with most common interaction queries.';
COMMENT ON FUNCTION update_ai_model_performance IS 'Tracks AI provider performance metrics for optimization.';
COMMENT ON FUNCTION normalize_interaction_check IS 'Normalizes substance names for consistent interaction checking.';
COMMENT ON COLUMN critical_interaction_rules.superseded_by IS 'References newer version of this rule if updated.';
COMMENT ON COLUMN feedback_reports.resulted_in_rule_id IS 'Links user feedback to implemented interaction rules.';

-- =====================================================
-- VERIFICATION
-- =====================================================

DO $$
BEGIN
    RAISE NOTICE '✅ Enhancements successfully applied!';
    RAISE NOTICE '📊 New indexes created for better performance';
    RAISE NOTICE '🔄 Cleanup functions ready for scheduling';
    RAISE NOTICE '📝 Versioning support added to interaction rules';
    RAISE NOTICE '🔗 Feedback tracking enhanced';
    RAISE NOTICE '👨‍⚕️ Provider verification fields added';
    RAISE NOTICE '🚀 Cache warming and analytics functions ready';
END $$;