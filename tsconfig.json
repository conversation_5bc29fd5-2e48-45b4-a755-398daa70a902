{"compilerOptions": {"target": "esnext", "lib": ["esnext", "dom"], "allowJs": true, "jsx": "react-native", "noEmit": true, "isolatedModules": true, "strict": true, "allowSyntheticDefaultImports": true, "esModuleInterop": true, "skipLibCheck": true, "resolveJsonModule": true, "baseUrl": ".", "paths": {"@/*": ["./src/*"], "@/services/*": ["./src/services/*"]}, "types": ["jest", "node"]}, "exclude": ["node_modules", "babel.config.js", "metro.config.js", "jest.config.js"], "extends": "expo/tsconfig.base", "include": ["**/*.ts", "**/*.tsx"]}